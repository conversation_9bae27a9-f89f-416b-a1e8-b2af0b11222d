version: '3.8'

# Development override for docker-compose.yml
# Use with: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  backend:
    build:
      target: development
    volumes:
      # Enhanced volume mounts for development with proper permissions
      - ./backend/src:/app/src:rw
      - ./backend/tests:/app/tests:rw
      - ./backend/config:/app/config:rw
      - ./backend/scripts:/app/scripts:rw
      - ./backend/requirements.txt:/app/requirements.txt:ro
      - ./backend/requirements-dev.txt:/app/requirements-dev.txt:ro
      # Persistent data (same as production)
      - user-projects:/app/user-projects:rw
      - backend-logs:/app/logs:rw
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - RELOAD=true
      - LOG_LEVEL=debug
      # CRITICAL FIX: Enhanced PYTHONPATH for development
      - PYTHONPATH=/app:/app/src
      # Development-specific settings
      - WORKERS=1
    command: ["uvicorn", "ai_coding_agent.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"]
    ports:
      - "8000:8000"
      - "5678:5678"  # Debug port for VS Code

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: build  # Use build stage for development
    volumes:
      # Mount entire source for hot reloading with proper permissions
      - ./frontend/src:/app/src:rw
      - ./frontend/public:/app/public:rw
      - ./frontend/package.json:/app/package.json:ro
      - ./frontend/package-lock.json:/app/package-lock.json:ro
      - ./frontend/tsconfig.json:/app/tsconfig.json:ro
      - ./frontend/tailwind.config.js:/app/tailwind.config.js:ro
      - ./frontend/postcss.config.js:/app/postcss.config.js:ro
      # Mount node_modules as volume for better performance
      - frontend-node-modules:/app/node_modules
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://localhost:8000
      - CHOKIDAR_USEPOLLING=true
      - FAST_REFRESH=true
      - WDS_SOCKET_HOST=localhost
      - WDS_SOCKET_PORT=3000
    command: ["npm", "start"]
    ports:
      - "3000:3000"  # Development server port

  # vector-db service removed - now using pgvector via Supabase

  postgres:
    environment:
      - POSTGRES_LOG_STATEMENT=all
      - POSTGRES_LOG_MIN_DURATION_STATEMENT=0
    volumes:
      # Add development database scripts
      - ./database/dev-init:/docker-entrypoint-initdb.d:ro
      - postgres-dev-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  # Development Tools
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ai-coding-agent-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "8080:80"
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    networks:
      - ai-coding-agent-network
    depends_on:
      - postgres

  # Redis for development caching (optional)
  redis:
    image: redis:7-alpine
    container_name: ai-coding-agent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - ai-coding-agent-network
    command: redis-server --appendonly yes

volumes:
  postgres-dev-data:
    driver: local
  pgadmin-data:
    driver: local
  redis-data:
    driver: local
  frontend-node-modules:
    driver: local
